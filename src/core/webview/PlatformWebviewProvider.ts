import { IWebview, IWebviewProvider, IWebviewOptions, WebviewEvent } from "../../platform/interfaces/IWebview"
import { IDisposable } from "../../platform/interfaces/IFileSystem"
import { IPlatformServices } from "../../platform/interfaces"

/**
 * Platform-agnostic webview provider that wraps the platform-specific implementation
 * This allows the same webview logic to work across VS Code and standalone environments
 */
export class PlatformWebviewProvider {
  private platformWebview: IWebviewProvider
  private platformServices: IPlatformServices
  private activeWebview?: IWebview
  private messageHandlers: Map<string, (message: any) => void> = new Map()
  private disposables: IDisposable[] = []

  constructor(platformServices: IPlatformServices) {
    this.platformServices = platformServices
    this.platformWebview = platformServices.webview
  }

  /**
   * Create a new webview with platform-agnostic options
   */
  async createWebview(options: IWebviewOptions): Promise<IWebview> {
    const webview = await this.platformWebview.createWebview(options)
    this.activeWebview = webview

    // Set up message handling
    const messageDisposable = webview.onMessage((message) => {
      this.handleMessage(message)
    })
    this.disposables.push(messageDisposable)

    return webview
  }

  /**
   * Get the currently active webview
   */
  getActiveWebview(): IWebview | undefined {
    return this.activeWebview || this.platformWebview.getActiveWebview()
  }

  /**
   * Register a message handler for a specific message type
   */
  onMessage(type: string, handler: (message: any) => void): IDisposable {
    this.messageHandlers.set(type, handler)
    
    return {
      dispose: () => {
        this.messageHandlers.delete(type)
      }
    }
  }

  /**
   * Post a message to the active webview
   */
  async postMessage(message: any): Promise<void> {
    const webview = this.getActiveWebview()
    if (webview) {
      await webview.postMessage(message)
    }
  }

  /**
   * Handle incoming messages from the webview
   */
  private handleMessage(message: any): void {
    const handler = this.messageHandlers.get(message.type)
    if (handler) {
      handler(message)
    } else {
      // Log unhandled message types for debugging
      console.warn(`Unhandled webview message type: ${message.type}`)
    }
  }

  /**
   * Register for webview lifecycle events
   */
  onWebviewEvent(event: WebviewEvent, callback: (webview: IWebview) => void): IDisposable {
    return this.platformWebview.onWebviewEvent(event, callback)
  }

  /**
   * Set HTML content for the active webview
   */
  setHtml(html: string): void {
    const webview = this.getActiveWebview()
    if (webview) {
      webview.setHtml(html)
    }
  }

  /**
   * Show the active webview
   */
  show(): void {
    const webview = this.getActiveWebview()
    if (webview) {
      webview.show()
    }
  }

  /**
   * Hide the active webview
   */
  hide(): void {
    const webview = this.getActiveWebview()
    if (webview) {
      webview.hide()
    }
  }

  /**
   * Check if the active webview is visible
   */
  isVisible(): boolean {
    const webview = this.getActiveWebview()
    return webview ? webview.isVisible() : false
  }

  /**
   * Dispose of all resources
   */
  dispose(): void {
    this.disposables.forEach(d => d.dispose())
    this.disposables = []
    this.messageHandlers.clear()
    
    if (this.activeWebview) {
      this.activeWebview.dispose()
      this.activeWebview = undefined
    }
  }
}

/**
 * Factory function to create a platform webview provider
 */
export function createPlatformWebviewProvider(platformServices: IPlatformServices): PlatformWebviewProvider {
  return new PlatformWebviewProvider(platformServices)
}

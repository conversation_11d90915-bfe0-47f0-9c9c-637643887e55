import fs from "fs/promises"
import * as path from "path"

import delay from "delay"

import { CommandExecutionStatus } from "@roo-code/types"
import { TelemetryService } from "@roo-code/telemetry"

import { Task } from "../task/Task"

import { ToolUse, Ask<PERSON><PERSON>roval, <PERSON>leError, PushToolResult, RemoveClosingTag, ToolResponse } from "../../shared/tools"
import { formatResponse } from "../prompts/responses"
import { unescapeHtmlEntities } from "../../utils/text-normalization"
// Terminal functionality removed in standalone version

class ShellIntegrationError extends Error {}

export async function executeCommandTool(
	cline: Task,
	block: ToolUse,
	askApproval: AskApproval,
	handleError: HandleError,
	pushToolResult: PushToolResult,
	removeClosingTag: RemoveClosingTag,
) {
	let command: string | undefined = block.params.command
	const customCwd: string | undefined = block.params.cwd

	try {
		if (block.partial) {
			await cline.ask("command", removeClosingTag("command", command), block.partial).catch(() => {})
			return
		} else {
			if (!command) {
				cline.consecutiveMistakeCount++
				cline.recordToolError("execute_command")
				pushToolResult(await cline.sayAndCreateMissingParamError("execute_command", "command"))
				return
			}

			const ignoredFileAttemptedToAccess = cline.rooIgnoreController?.validateCommand(command)

			if (ignoredFileAttemptedToAccess) {
				await cline.say("rooignore_error", ignoredFileAttemptedToAccess)
				pushToolResult(formatResponse.toolError(formatResponse.rooIgnoreError(ignoredFileAttemptedToAccess)))
				return
			}

			cline.consecutiveMistakeCount = 0

			command = unescapeHtmlEntities(command) // Unescape HTML entities.
			const didApprove = await askApproval("command", command)

			if (!didApprove) {
				return
			}

			const executionId = cline.lastMessageTs?.toString() ?? Date.now().toString()
			const clineProvider = await cline.providerRef.deref()
			const clineProviderState = await clineProvider?.getState()
			const { terminalOutputLineLimit = 500, terminalShellIntegrationDisabled = false } = clineProviderState ?? {}

			const options: ExecuteCommandOptions = {
				executionId,
				command,
				customCwd,
				terminalShellIntegrationDisabled,
				terminalOutputLineLimit,
			}

			try {
				const [rejected, result] = await executeCommand(cline, options)

				if (rejected) {
					cline.didRejectTool = true
				}

				pushToolResult(result)
			} catch (error: unknown) {
				const status: CommandExecutionStatus = { executionId, status: "fallback" }
				clineProvider?.postMessageToWebview({ type: "commandExecutionStatus", text: JSON.stringify(status) })
				await cline.say("shell_integration_warning")

				if (error instanceof ShellIntegrationError) {
					const [rejected, result] = await executeCommand(cline, {
						...options,
						terminalShellIntegrationDisabled: true,
					})

					if (rejected) {
						cline.didRejectTool = true
					}

					pushToolResult(result)
				} else {
					pushToolResult(`Command failed to execute in terminal due to a shell integration error.`)
				}
			}

			return
		}
	} catch (error) {
		await handleError("executing command", error)
		return
	}
}

export type ExecuteCommandOptions = {
	executionId: string
	command: string
	customCwd?: string
	terminalShellIntegrationDisabled?: boolean
	terminalOutputLineLimit?: number
}

export async function executeCommand(
	cline: Task,
	{
		executionId,
		command,
		customCwd,
		terminalShellIntegrationDisabled = false,
		terminalOutputLineLimit = 500,
	}: ExecuteCommandOptions,
): Promise<[boolean, ToolResponse]> {
	// Terminal functionality has been removed in standalone version
	return [
		false,
		formatResponse.toolResult(
			"Terminal command execution is not available in the standalone version of Roo Code. " +
			"This functionality has been removed to support platform independence. " +
			`Command that was attempted: ${command}`
		)
	]
	// All terminal execution logic removed
}

/**
 * Platform-agnostic file system interface
 * Abstracts file operations to work across VS Code and standalone environments
 */
export interface IFileSystem {
  /**
   * Read the contents of a file
   * @param path - Absolute or relative file path
   * @returns Promise resolving to file contents as string
   */
  readFile(path: string): Promise<string>

  /**
   * Write content to a file
   * @param path - Absolute or relative file path
   * @param content - Content to write
   * @returns Promise that resolves when write is complete
   */
  writeFile(path: string, content: string): Promise<void>

  /**
   * Check if a file or directory exists
   * @param path - Path to check
   * @returns Promise resolving to true if exists, false otherwise
   */
  exists(path: string): Promise<boolean>

  /**
   * List files and directories in a directory
   * @param path - Directory path
   * @param recursive - Whether to list recursively
   * @returns Promise resolving to array of file/directory paths
   */
  listFiles(path: string, recursive?: boolean): Promise<string[]>

  /**
   * Watch for file system changes
   * @param pattern - Glob pattern to watch
   * @param callback - Callback function called when files change
   * @returns Disposable to stop watching
   */
  watchFiles(pattern: string, callback: (path: string, event: 'create' | 'change' | 'delete') => void): IDisposable

  /**
   * Get file statistics
   * @param path - File path
   * @returns Promise resolving to file stats
   */
  stat(path: string): Promise<IFileStats>

  /**
   * Create a directory
   * @param path - Directory path
   * @returns Promise that resolves when directory is created
   */
  createDirectory(path: string): Promise<void>

  /**
   * Delete a file or directory
   * @param path - Path to delete
   * @param recursive - Whether to delete recursively for directories
   * @returns Promise that resolves when deletion is complete
   */
  delete(path: string, recursive?: boolean): Promise<void>

  /**
   * Copy a file or directory
   * @param source - Source path
   * @param destination - Destination path
   * @returns Promise that resolves when copy is complete
   */
  copy(source: string, destination: string): Promise<void>

  /**
   * Move/rename a file or directory
   * @param source - Source path
   * @param destination - Destination path
   * @returns Promise that resolves when move is complete
   */
  move(source: string, destination: string): Promise<void>
}

/**
 * File statistics interface
 */
export interface IFileStats {
  isFile(): boolean
  isDirectory(): boolean
  size: number
  mtime: Date
  ctime: Date
}

/**
 * Disposable interface for cleanup
 */
export interface IDisposable {
  dispose(): void
}

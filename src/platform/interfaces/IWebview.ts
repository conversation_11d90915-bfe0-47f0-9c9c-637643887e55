import { IDisposable } from './IFileSystem'

/**
 * Platform-agnostic webview interface
 * Abstracts UI communication to work across VS Code webviews and web applications
 */
export interface IWebview {
  /**
   * Post a message to the webview/frontend
   * @param message - Message object to send
   * @returns Promise that resolves when message is sent
   */
  postMessage(message: any): Promise<void>

  /**
   * Register a callback for messages from the webview/frontend
   * @param callback - Function to call when message is received
   * @returns Disposable to unregister the callback
   */
  onMessage(callback: (message: any) => void): IDisposable

  /**
   * Set the HTML content of the webview
   * @param html - HTML content string
   */
  setHtml(html: string): void

  /**
   * Get the current visibility state
   * @returns True if webview is visible, false otherwise
   */
  isVisible(): boolean

  /**
   * Show the webview
   */
  show(): void

  /**
   * Hide the webview
   */
  hide(): void

  /**
   * Dispose of the webview and clean up resources
   */
  dispose(): void
}

/**
 * Webview provider interface for creating and managing webviews
 */
export interface IWebviewProvider {
  /**
   * Create a new webview instance
   * @param options - Configuration options for the webview
   * @returns Promise resolving to the created webview
   */
  createWebview(options: IWebviewOptions): Promise<IWebview>

  /**
   * Get the currently active webview, if any
   * @returns Active webview or undefined
   */
  getActiveWebview(): IWebview | undefined

  /**
   * Register for webview lifecycle events
   * @param event - Event type to listen for
   * @param callback - Callback function
   * @returns Disposable to unregister the listener
   */
  onWebviewEvent(event: WebviewEvent, callback: (webview: IWebview) => void): IDisposable
}

/**
 * Configuration options for creating webviews
 */
export interface IWebviewOptions {
  /**
   * Title of the webview
   */
  title: string

  /**
   * Whether to retain context when hidden
   */
  retainContextWhenHidden?: boolean

  /**
   * Whether scripts are enabled
   */
  enableScripts?: boolean

  /**
   * Local resource roots for loading assets
   */
  localResourceRoots?: string[]

  /**
   * Content Security Policy
   */
  csp?: string

  /**
   * Initial HTML content
   */
  html?: string
}

/**
 * Webview lifecycle events
 */
export type WebviewEvent = 'created' | 'shown' | 'hidden' | 'disposed'

/**
 * Message types for webview communication
 */
export interface WebviewMessage {
  type: string
  [key: string]: any
}

/**
 * Platform abstraction interfaces
 * 
 * This module exports all platform abstraction interfaces that allow
 * roo-code to work across different environments (VS Code extension,
 * standalone application, web application, etc.)
 */

export * from './IFileSystem'
export * from './IWebview'
export * from './IConfiguration'
export * from './ILogger'
export * from './IWorkspace'

/**
 * Platform services container interface
 * Provides access to all platform-specific services
 */
export interface IPlatformServices {
  /**
   * File system operations
   */
  fileSystem: import('./IFileSystem').IFileSystem

  /**
   * Webview/UI operations
   */
  webview: import('./IWebview').IWebviewProvider

  /**
   * Configuration management
   */
  configuration: import('./IConfiguration').IConfiguration

  /**
   * Logging services
   */
  logger: import('./ILogger').ILoggerFactory

  /**
   * Workspace operations
   */
  workspace: import('./IWorkspace').IWorkspace

  /**
   * Platform type identifier
   */
  platformType: PlatformType

  /**
   * Initialize the platform services
   * @param options - Platform-specific initialization options
   * @returns Promise that resolves when initialization is complete
   */
  initialize(options?: PlatformInitOptions): Promise<void>

  /**
   * Dispose of platform services and clean up resources
   * @returns Promise that resolves when cleanup is complete
   */
  dispose(): Promise<void>
}

/**
 * Platform type enumeration
 */
export enum PlatformType {
  /**
   * VS Code extension environment
   */
  VSCode = 'vscode',

  /**
   * Standalone desktop application
   */
  Standalone = 'standalone',

  /**
   * Web application
   */
  Web = 'web',

  /**
   * Node.js server environment
   */
  Server = 'server'
}

/**
 * Platform initialization options
 */
export interface PlatformInitOptions {
  /**
   * Working directory for file operations
   */
  workingDirectory?: string

  /**
   * Configuration file path
   */
  configPath?: string

  /**
   * Log level
   */
  logLevel?: import('./ILogger').LogLevel

  /**
   * Additional platform-specific options
   */
  [key: string]: any
}

/**
 * Platform factory interface for creating platform services
 */
export interface IPlatformFactory {
  /**
   * Create platform services for the specified type
   * @param type - Platform type
   * @param options - Initialization options
   * @returns Platform services instance
   */
  createPlatformServices(type: PlatformType, options?: PlatformInitOptions): Promise<IPlatformServices>

  /**
   * Detect the current platform type
   * @returns Detected platform type
   */
  detectPlatformType(): PlatformType

  /**
   * Check if a platform type is supported
   * @param type - Platform type to check
   * @returns True if supported, false otherwise
   */
  isPlatformSupported(type: PlatformType): boolean
}

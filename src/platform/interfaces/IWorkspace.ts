import { IDisposable } from './IFileSystem'

/**
 * Platform-agnostic workspace interface
 * Abstracts workspace operations to work across VS Code and standalone environments
 */
export interface IWorkspace {
  /**
   * Get the root path of the workspace
   * @returns Workspace root path or undefined if no workspace is open
   */
  getRootPath(): string | undefined

  /**
   * Get all workspace folders
   * @returns Array of workspace folder information
   */
  getWorkspaceFolders(): IWorkspaceFolder[]

  /**
   * Get the currently active file path
   * @returns Active file path or undefined if no file is active
   */
  getActiveFilePath(): string | undefined

  /**
   * Get all open file paths
   * @returns Array of open file paths
   */
  getOpenFilePaths(): string[]

  /**
   * Open a file in the workspace
   * @param path - File path to open
   * @param options - Options for opening the file
   * @returns Promise that resolves when file is opened
   */
  openFile(path: string, options?: OpenFileOptions): Promise<void>

  /**
   * Close a file in the workspace
   * @param path - File path to close
   * @returns Promise that resolves when file is closed
   */
  closeFile(path: string): Promise<void>

  /**
   * Get the selected text in the active file
   * @returns Selected text or undefined if no selection
   */
  getSelectedText(): string | undefined

  /**
   * Get the current cursor position in the active file
   * @returns Cursor position or undefined if no active file
   */
  getCursorPosition(): IPosition | undefined

  /**
   * Set the cursor position in the active file
   * @param position - Position to set cursor to
   * @returns Promise that resolves when cursor is set
   */
  setCursorPosition(position: IPosition): Promise<void>

  /**
   * Insert text at the current cursor position
   * @param text - Text to insert
   * @returns Promise that resolves when text is inserted
   */
  insertText(text: string): Promise<void>

  /**
   * Replace text in a range
   * @param range - Range to replace
   * @param text - New text
   * @returns Promise that resolves when text is replaced
   */
  replaceText(range: IRange, text: string): Promise<void>

  /**
   * Register for workspace events
   * @param event - Event type to listen for
   * @param callback - Callback function
   * @returns Disposable to unregister the listener
   */
  onWorkspaceEvent(event: WorkspaceEvent, callback: (data: any) => void): IDisposable

  /**
   * Show a notification to the user
   * @param message - Message to show
   * @param type - Notification type
   * @param actions - Optional action buttons
   * @returns Promise resolving to selected action or undefined
   */
  showNotification(message: string, type: NotificationType, actions?: string[]): Promise<string | undefined>

  /**
   * Show an input box to the user
   * @param options - Input box options
   * @returns Promise resolving to user input or undefined if cancelled
   */
  showInputBox(options: InputBoxOptions): Promise<string | undefined>

  /**
   * Show a quick pick to the user
   * @param items - Items to choose from
   * @param options - Quick pick options
   * @returns Promise resolving to selected item or undefined if cancelled
   */
  showQuickPick<T>(items: T[], options: QuickPickOptions<T>): Promise<T | undefined>
}

/**
 * Workspace folder information
 */
export interface IWorkspaceFolder {
  /**
   * Name of the workspace folder
   */
  name: string

  /**
   * Path to the workspace folder
   */
  path: string

  /**
   * Index of the workspace folder
   */
  index: number
}

/**
 * File position interface
 */
export interface IPosition {
  /**
   * Line number (0-based)
   */
  line: number

  /**
   * Character position (0-based)
   */
  character: number
}

/**
 * Text range interface
 */
export interface IRange {
  /**
   * Start position
   */
  start: IPosition

  /**
   * End position
   */
  end: IPosition
}

/**
 * Options for opening files
 */
export interface OpenFileOptions {
  /**
   * Whether to show the file in the editor
   */
  show?: boolean

  /**
   * Position to place cursor at
   */
  position?: IPosition

  /**
   * Range to select
   */
  selection?: IRange

  /**
   * Whether to preserve focus
   */
  preserveFocus?: boolean
}

/**
 * Workspace events
 */
export type WorkspaceEvent = 
  | 'fileOpened'
  | 'fileClosed'
  | 'fileChanged'
  | 'selectionChanged'
  | 'cursorPositionChanged'
  | 'workspaceFolderAdded'
  | 'workspaceFolderRemoved'

/**
 * Notification types
 */
export enum NotificationType {
  Info = 'info',
  Warning = 'warning',
  Error = 'error'
}

/**
 * Input box options
 */
export interface InputBoxOptions {
  /**
   * Prompt text to show
   */
  prompt?: string

  /**
   * Placeholder text
   */
  placeholder?: string

  /**
   * Default value
   */
  value?: string

  /**
   * Whether input is a password
   */
  password?: boolean

  /**
   * Validation function
   */
  validateInput?: (value: string) => string | undefined
}

/**
 * Quick pick options
 */
export interface QuickPickOptions<T> {
  /**
   * Placeholder text
   */
  placeholder?: string

  /**
   * Whether multiple selection is allowed
   */
  canPickMany?: boolean

  /**
   * Function to get display text for items
   */
  getLabel?: (item: T) => string

  /**
   * Function to get description for items
   */
  getDescription?: (item: T) => string

  /**
   * Function to get detail text for items
   */
  getDetail?: (item: T) => string
}

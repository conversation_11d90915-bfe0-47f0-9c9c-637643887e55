import { IDisposable } from './IFileSystem'

/**
 * Platform-agnostic configuration interface
 * Abstracts settings management to work across VS Code and standalone environments
 */
export interface IConfiguration {
  /**
   * Get a configuration value
   * @param key - Configuration key (supports dot notation)
   * @param defaultValue - Default value if key doesn't exist
   * @returns Configuration value or default
   */
  get<T>(key: string, defaultValue?: T): T | undefined

  /**
   * Set a configuration value
   * @param key - Configuration key (supports dot notation)
   * @param value - Value to set
   * @param scope - Configuration scope (global, workspace, etc.)
   * @returns Promise that resolves when value is set
   */
  set<T>(key: string, value: T, scope?: ConfigurationScope): Promise<void>

  /**
   * Check if a configuration key exists
   * @param key - Configuration key to check
   * @returns True if key exists, false otherwise
   */
  has(key: string): boolean

  /**
   * Delete a configuration key
   * @param key - Configuration key to delete
   * @param scope - Configuration scope
   * @returns Promise that resolves when key is deleted
   */
  delete(key: string, scope?: ConfigurationScope): Promise<void>

  /**
   * Get all configuration keys and values
   * @param section - Optional section to filter by
   * @returns Object containing all configuration
   */
  getAll(section?: string): Record<string, any>

  /**
   * Register a callback for configuration changes
   * @param key - Configuration key to watch (optional, watches all if not provided)
   * @param callback - Function to call when configuration changes
   * @returns Disposable to unregister the callback
   */
  onDidChange(key: string | undefined, callback: (key: string, value: any, oldValue: any) => void): IDisposable

  /**
   * Reload configuration from source
   * @returns Promise that resolves when configuration is reloaded
   */
  reload(): Promise<void>

  /**
   * Save configuration to persistent storage
   * @returns Promise that resolves when configuration is saved
   */
  save(): Promise<void>
}

/**
 * Configuration scope enumeration
 */
export enum ConfigurationScope {
  /**
   * Global configuration (applies to all workspaces)
   */
  Global = 'global',

  /**
   * Workspace configuration (applies to current workspace)
   */
  Workspace = 'workspace',

  /**
   * User configuration (user-specific settings)
   */
  User = 'user'
}

/**
 * Configuration change event
 */
export interface ConfigurationChangeEvent {
  /**
   * The configuration key that changed
   */
  key: string

  /**
   * The new value
   */
  value: any

  /**
   * The previous value
   */
  oldValue: any

  /**
   * The scope where the change occurred
   */
  scope: ConfigurationScope
}

/**
 * Configuration provider interface for different storage backends
 */
export interface IConfigurationProvider {
  /**
   * Load configuration from storage
   * @returns Promise resolving to configuration object
   */
  load(): Promise<Record<string, any>>

  /**
   * Save configuration to storage
   * @param config - Configuration object to save
   * @returns Promise that resolves when save is complete
   */
  save(config: Record<string, any>): Promise<void>

  /**
   * Watch for external configuration changes
   * @param callback - Function to call when configuration changes externally
   * @returns Disposable to stop watching
   */
  watch(callback: () => void): IDisposable
}

/**
 * Configuration validation interface
 */
export interface IConfigurationValidator {
  /**
   * Validate a configuration value
   * @param key - Configuration key
   * @param value - Value to validate
   * @returns Validation result
   */
  validate(key: string, value: any): ConfigurationValidationResult
}

/**
 * Configuration validation result
 */
export interface ConfigurationValidationResult {
  /**
   * Whether the value is valid
   */
  valid: boolean

  /**
   * Error message if validation failed
   */
  error?: string

  /**
   * Sanitized/corrected value
   */
  sanitizedValue?: any
}

import { IDisposable } from './IFileSystem'

/**
 * Log level enumeration
 */
export enum LogLevel {
  Trace = 0,
  Debug = 1,
  Info = 2,
  Warn = 3,
  Error = 4,
  Fatal = 5
}

/**
 * Platform-agnostic logger interface
 * Abstracts logging to work across VS Code output channels and file-based logging
 */
export interface ILogger {
  /**
   * Log a trace message
   * @param message - Message to log
   * @param ...args - Additional arguments
   */
  trace(message: string, ...args: any[]): void

  /**
   * Log a debug message
   * @param message - Message to log
   * @param ...args - Additional arguments
   */
  debug(message: string, ...args: any[]): void

  /**
   * Log an info message
   * @param message - Message to log
   * @param ...args - Additional arguments
   */
  info(message: string, ...args: any[]): void

  /**
   * Log a warning message
   * @param message - Message to log
   * @param ...args - Additional arguments
   */
  warn(message: string, ...args: any[]): void

  /**
   * Log an error message
   * @param message - Message to log
   * @param error - Optional error object
   * @param ...args - Additional arguments
   */
  error(message: string, error?: Error, ...args: any[]): void

  /**
   * Log a fatal message
   * @param message - Message to log
   * @param error - Optional error object
   * @param ...args - Additional arguments
   */
  fatal(message: string, error?: Error, ...args: any[]): void

  /**
   * Log a message at the specified level
   * @param level - Log level
   * @param message - Message to log
   * @param ...args - Additional arguments
   */
  log(level: LogLevel, message: string, ...args: any[]): void

  /**
   * Set the minimum log level
   * @param level - Minimum log level
   */
  setLevel(level: LogLevel): void

  /**
   * Get the current log level
   * @returns Current log level
   */
  getLevel(): LogLevel

  /**
   * Check if a log level is enabled
   * @param level - Log level to check
   * @returns True if level is enabled, false otherwise
   */
  isLevelEnabled(level: LogLevel): boolean

  /**
   * Create a child logger with a specific context
   * @param context - Context name for the child logger
   * @returns Child logger instance
   */
  child(context: string): ILogger

  /**
   * Flush any pending log messages
   * @returns Promise that resolves when flush is complete
   */
  flush(): Promise<void>

  /**
   * Dispose of the logger and clean up resources
   */
  dispose(): void
}

/**
 * Logger factory interface for creating loggers
 */
export interface ILoggerFactory {
  /**
   * Create a logger instance
   * @param name - Logger name/context
   * @param options - Logger configuration options
   * @returns Logger instance
   */
  createLogger(name: string, options?: LoggerOptions): ILogger

  /**
   * Get an existing logger by name
   * @param name - Logger name
   * @returns Logger instance or undefined if not found
   */
  getLogger(name: string): ILogger | undefined

  /**
   * Set global log level for all loggers
   * @param level - Log level to set
   */
  setGlobalLevel(level: LogLevel): void

  /**
   * Register a log appender
   * @param appender - Log appender to register
   * @returns Disposable to unregister the appender
   */
  addAppender(appender: ILogAppender): IDisposable
}

/**
 * Logger configuration options
 */
export interface LoggerOptions {
  /**
   * Minimum log level for this logger
   */
  level?: LogLevel

  /**
   * Whether to include timestamps in log messages
   */
  includeTimestamp?: boolean

  /**
   * Whether to include the logger name in log messages
   */
  includeName?: boolean

  /**
   * Custom log format function
   */
  formatter?: LogFormatter

  /**
   * Additional metadata to include with all log messages
   */
  metadata?: Record<string, any>
}

/**
 * Log message interface
 */
export interface LogMessage {
  /**
   * Log level
   */
  level: LogLevel

  /**
   * Log message
   */
  message: string

  /**
   * Logger name/context
   */
  logger: string

  /**
   * Timestamp when message was logged
   */
  timestamp: Date

  /**
   * Additional arguments
   */
  args: any[]

  /**
   * Error object if applicable
   */
  error?: Error

  /**
   * Additional metadata
   */
  metadata?: Record<string, any>
}

/**
 * Log formatter function type
 */
export type LogFormatter = (message: LogMessage) => string

/**
 * Log appender interface for different output destinations
 */
export interface ILogAppender {
  /**
   * Append a log message
   * @param message - Log message to append
   */
  append(message: LogMessage): void

  /**
   * Flush any pending messages
   * @returns Promise that resolves when flush is complete
   */
  flush(): Promise<void>

  /**
   * Dispose of the appender and clean up resources
   */
  dispose(): void
}

import * as vscode from "vscode"
import { 
  ILogger, 
  ILoggerFactory, 
  ILogAppender, 
  LogLevel, 
  LogMessage, 
  LoggerOptions, 
  LogFormatter 
} from "../interfaces/ILogger"
import { IDisposable } from "../interfaces/IFileSystem"

/**
 * VS Code implementation of the logger interface
 * Uses VS Code output channels for logging
 */
export class VSCodeLogger implements ILogger {
  private outputChannel: vscode.OutputChannel
  private level: LogLevel
  private context: string
  private includeTimestamp: boolean
  private includeName: boolean
  private formatter?: LogFormatter
  private metadata: Record<string, any>

  constructor(
    name: string, 
    outputChannel: vscode.OutputChannel, 
    options: LoggerOptions = {}
  ) {
    this.outputChannel = outputChannel
    this.context = name
    this.level = options.level ?? LogLevel.Info
    this.includeTimestamp = options.includeTimestamp ?? true
    this.includeName = options.includeName ?? true
    this.formatter = options.formatter
    this.metadata = options.metadata ?? {}
  }

  trace(message: string, ...args: any[]): void {
    this.log(LogLevel.Trace, message, ...args)
  }

  debug(message: string, ...args: any[]): void {
    this.log(LogLevel.Debug, message, ...args)
  }

  info(message: string, ...args: any[]): void {
    this.log(LogLevel.Info, message, ...args)
  }

  warn(message: string, ...args: any[]): void {
    this.log(LogLevel.Warn, message, ...args)
  }

  error(message: string, error?: Error, ...args: any[]): void {
    const allArgs = error ? [error, ...args] : args
    this.log(LogLevel.Error, message, ...allArgs)
  }

  fatal(message: string, error?: Error, ...args: any[]): void {
    const allArgs = error ? [error, ...args] : args
    this.log(LogLevel.Fatal, message, ...allArgs)
  }

  log(level: LogLevel, message: string, ...args: any[]): void {
    if (!this.isLevelEnabled(level)) {
      return
    }

    const logMessage: LogMessage = {
      level,
      message,
      logger: this.context,
      timestamp: new Date(),
      args,
      error: args.find(arg => arg instanceof Error),
      metadata: this.metadata
    }

    const formattedMessage = this.formatter 
      ? this.formatter(logMessage)
      : this.defaultFormatter(logMessage)

    this.outputChannel.appendLine(formattedMessage)
  }

  setLevel(level: LogLevel): void {
    this.level = level
  }

  getLevel(): LogLevel {
    return this.level
  }

  isLevelEnabled(level: LogLevel): boolean {
    return level >= this.level
  }

  child(context: string): ILogger {
    const childName = `${this.context}.${context}`
    return new VSCodeLogger(childName, this.outputChannel, {
      level: this.level,
      includeTimestamp: this.includeTimestamp,
      includeName: this.includeName,
      formatter: this.formatter,
      metadata: { ...this.metadata }
    })
  }

  async flush(): Promise<void> {
    // VS Code output channels don't need explicit flushing
  }

  dispose(): void {
    // Don't dispose the output channel as it might be shared
  }

  private defaultFormatter(logMessage: LogMessage): string {
    const parts: string[] = []

    if (this.includeTimestamp) {
      parts.push(`[${logMessage.timestamp.toISOString()}]`)
    }

    parts.push(`[${LogLevel[logMessage.level].toUpperCase()}]`)

    if (this.includeName) {
      parts.push(`[${logMessage.logger}]`)
    }

    parts.push(logMessage.message)

    if (logMessage.args.length > 0) {
      const argsStr = logMessage.args
        .map(arg => {
          if (arg instanceof Error) {
            return `\n${arg.stack || arg.message}`
          }
          return typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        })
        .join(' ')
      parts.push(argsStr)
    }

    return parts.join(' ')
  }
}

/**
 * VS Code implementation of the logger factory
 */
export class VSCodeLoggerFactory implements ILoggerFactory {
  private loggers: Map<string, VSCodeLogger> = new Map()
  private outputChannels: Map<string, vscode.OutputChannel> = new Map()
  private globalLevel: LogLevel = LogLevel.Info
  private appenders: ILogAppender[] = []

  createLogger(name: string, options: LoggerOptions = {}): ILogger {
    if (this.loggers.has(name)) {
      return this.loggers.get(name)!
    }

    // Create or reuse output channel
    let outputChannel = this.outputChannels.get(name)
    if (!outputChannel) {
      outputChannel = vscode.window.createOutputChannel(name)
      this.outputChannels.set(name, outputChannel)
    }

    const logger = new VSCodeLogger(name, outputChannel, {
      level: options.level ?? this.globalLevel,
      ...options
    })

    this.loggers.set(name, logger)
    return logger
  }

  getLogger(name: string): ILogger | undefined {
    return this.loggers.get(name)
  }

  setGlobalLevel(level: LogLevel): void {
    this.globalLevel = level
    // Update existing loggers
    for (const logger of this.loggers.values()) {
      logger.setLevel(level)
    }
  }

  addAppender(appender: ILogAppender): IDisposable {
    this.appenders.push(appender)
    
    return {
      dispose: () => {
        const index = this.appenders.indexOf(appender)
        if (index >= 0) {
          this.appenders.splice(index, 1)
        }
      }
    }
  }

  dispose(): void {
    // Dispose all loggers
    for (const logger of this.loggers.values()) {
      logger.dispose()
    }
    this.loggers.clear()

    // Dispose output channels
    for (const channel of this.outputChannels.values()) {
      channel.dispose()
    }
    this.outputChannels.clear()

    // Dispose appenders
    for (const appender of this.appenders) {
      appender.dispose()
    }
    this.appenders = []
  }
}

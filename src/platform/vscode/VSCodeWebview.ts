import * as vscode from "vscode"
import { 
  IWebview, 
  IWebviewProvider, 
  IWebviewOptions, 
  WebviewEvent, 
  WebviewMessage 
} from "../interfaces/IWebview"
import { IDisposable } from "../interfaces/IFileSystem"

/**
 * VS Code implementation of the webview interface
 * Wraps VS Code webview APIs
 */
export class VSCodeWebview implements IWebview {
  private webview: vscode.Webview
  private panel?: vscode.WebviewPanel
  protected view?: vscode.WebviewView
  private messageListeners: ((message: any) => void)[] = []
  private disposables: vscode.Disposable[] = []

  constructor(webview: vscode.Webview, panel?: vscode.WebviewPanel, view?: vscode.WebviewView) {
    this.webview = webview
    this.panel = panel
    this.view = view
    
    this.setupMessageHandling()
  }

  async postMessage(message: any): Promise<void> {
    await this.webview.postMessage(message)
  }

  onMessage(callback: (message: any) => void): IDisposable {
    this.messageListeners.push(callback)
    
    return {
      dispose: () => {
        const index = this.messageListeners.indexOf(callback)
        if (index >= 0) {
          this.messageListeners.splice(index, 1)
        }
      }
    }
  }

  setHtml(html: string): void {
    this.webview.html = html
  }

  isVisible(): boolean {
    if (this.panel) {
      return this.panel.visible
    }
    if (this.view) {
      return this.view.visible
    }
    return false
  }

  show(): void {
    if (this.panel) {
      this.panel.reveal()
    }
    if (this.view) {
      this.view.show?.(true)
    }
  }

  hide(): void {
    if (this.panel) {
      this.panel.dispose()
    }
    // Views can't be hidden directly in VS Code
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose())
    this.disposables = []
    this.messageListeners = []
    
    if (this.panel) {
      this.panel.dispose()
    }
  }

  private setupMessageHandling(): void {
    const messageDisposable = this.webview.onDidReceiveMessage(message => {
      this.messageListeners.forEach(listener => listener(message))
    })
    
    this.disposables.push(messageDisposable)
  }
}

/**
 * VS Code implementation of the webview provider
 */
export class VSCodeWebviewProvider implements IWebviewProvider {
  private context: vscode.ExtensionContext
  private activeWebview?: VSCodeWebview
  private eventListeners: Map<WebviewEvent, ((webview: IWebview) => void)[]> = new Map()

  constructor(context: vscode.ExtensionContext) {
    this.context = context
  }

  async createWebview(options: IWebviewOptions): Promise<IWebview> {
    // Create webview panel
    const panel = vscode.window.createWebviewPanel(
      'roo-code-webview',
      options.title,
      vscode.ViewColumn.One,
      {
        enableScripts: options.enableScripts ?? true,
        retainContextWhenHidden: options.retainContextWhenHidden ?? true,
        localResourceRoots: options.localResourceRoots?.map(root => vscode.Uri.file(root)) || [this.context.extensionUri]
      }
    )

    // Set initial HTML if provided
    if (options.html) {
      panel.webview.html = options.html
    }

    // Set CSP if provided
    if (options.csp) {
      // CSP is typically set in the HTML content itself in VS Code
    }

    const webview = new VSCodeWebview(panel.webview, panel)
    this.activeWebview = webview

    // Setup panel event handlers
    panel.onDidDispose(() => {
      this.notifyEventListeners('disposed', webview)
      if (this.activeWebview === webview) {
        this.activeWebview = undefined
      }
    })

    panel.onDidChangeViewState(e => {
      if (e.webviewPanel.visible) {
        this.notifyEventListeners('shown', webview)
      } else {
        this.notifyEventListeners('hidden', webview)
      }
    })

    this.notifyEventListeners('created', webview)
    
    return webview
  }

  getActiveWebview(): IWebview | undefined {
    return this.activeWebview
  }

  onWebviewEvent(event: WebviewEvent, callback: (webview: IWebview) => void): IDisposable {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    
    this.eventListeners.get(event)!.push(callback)
    
    return {
      dispose: () => {
        const listeners = this.eventListeners.get(event)
        if (listeners) {
          const index = listeners.indexOf(callback)
          if (index >= 0) {
            listeners.splice(index, 1)
          }
        }
      }
    }
  }

  private notifyEventListeners(event: WebviewEvent, webview: IWebview): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => callback(webview))
    }
  }

  dispose(): void {
    this.activeWebview?.dispose()
    this.eventListeners.clear()
  }
}

/**
 * Adapter for existing VS Code WebviewView to work with the IWebview interface
 */
export class VSCodeWebviewViewAdapter extends VSCodeWebview {
  private webviewView: vscode.WebviewView

  constructor(webviewView: vscode.WebviewView) {
    super(webviewView.webview, undefined, webviewView)
    this.webviewView = webviewView
  }

  override isVisible(): boolean {
    return this.webviewView.visible
  }

  override show(): void {
    this.webviewView.show?.(true)
  }

  override hide(): void {
    // WebviewViews can't be hidden directly in VS Code
  }
}

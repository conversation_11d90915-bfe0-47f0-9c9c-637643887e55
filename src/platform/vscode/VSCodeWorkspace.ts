import * as vscode from "vscode"
import { 
  IWorkspace, 
  IWorkspaceFolder, 
  IPosition, 
  IRange, 
  OpenFileOptions, 
  WorkspaceEvent, 
  NotificationType, 
  InputBoxOptions, 
  QuickPickOptions 
} from "../interfaces/IWorkspace"
import { IDisposable } from "../interfaces/IFileSystem"

/**
 * VS Code implementation of the workspace interface
 * Wraps VS Code workspace and window APIs
 */
export class VSCodeWorkspace implements IWorkspace {
  private eventListeners: Map<WorkspaceEvent, ((data: any) => void)[]> = new Map()
  private disposables: vscode.Disposable[] = []

  constructor() {
    this.setupEventListeners()
  }

  getRootPath(): string | undefined {
    const workspaceFolders = vscode.workspace.workspaceFolders
    return workspaceFolders && workspaceFolders.length > 0 
      ? workspaceFolders[0].uri.fsPath 
      : undefined
  }

  getWorkspaceFolders(): IWorkspaceFolder[] {
    const folders = vscode.workspace.workspaceFolders || []
    return folders.map((folder, index) => ({
      name: folder.name,
      path: folder.uri.fsPath,
      index
    }))
  }

  getActiveFilePath(): string | undefined {
    return vscode.window.activeTextEditor?.document.uri.fsPath
  }

  getOpenFilePaths(): string[] {
    return vscode.window.visibleTextEditors.map(editor => editor.document.uri.fsPath)
  }

  async openFile(path: string, options: OpenFileOptions = {}): Promise<void> {
    const uri = vscode.Uri.file(path)
    const document = await vscode.workspace.openTextDocument(uri)
    
    const showOptions: vscode.TextDocumentShowOptions = {
      preserveFocus: options.preserveFocus,
      preview: false
    }

    if (options.position) {
      showOptions.selection = new vscode.Range(
        options.position.line,
        options.position.character,
        options.position.line,
        options.position.character
      )
    } else if (options.selection) {
      showOptions.selection = new vscode.Range(
        options.selection.start.line,
        options.selection.start.character,
        options.selection.end.line,
        options.selection.end.character
      )
    }

    if (options.show !== false) {
      await vscode.window.showTextDocument(document, showOptions)
    }
  }

  async closeFile(path: string): Promise<void> {
    const uri = vscode.Uri.file(path)
    const editor = vscode.window.visibleTextEditors.find(
      editor => editor.document.uri.fsPath === uri.fsPath
    )
    
    if (editor) {
      await vscode.commands.executeCommand('workbench.action.closeActiveEditor')
    }
  }

  getSelectedText(): string | undefined {
    const editor = vscode.window.activeTextEditor
    if (!editor || editor.selection.isEmpty) {
      return undefined
    }
    
    return editor.document.getText(editor.selection)
  }

  getCursorPosition(): IPosition | undefined {
    const editor = vscode.window.activeTextEditor
    if (!editor) {
      return undefined
    }
    
    return {
      line: editor.selection.active.line,
      character: editor.selection.active.character
    }
  }

  async setCursorPosition(position: IPosition): Promise<void> {
    const editor = vscode.window.activeTextEditor
    if (!editor) {
      throw new Error('No active text editor')
    }
    
    const vsPosition = new vscode.Position(position.line, position.character)
    editor.selection = new vscode.Selection(vsPosition, vsPosition)
  }

  async insertText(text: string): Promise<void> {
    const editor = vscode.window.activeTextEditor
    if (!editor) {
      throw new Error('No active text editor')
    }
    
    await editor.edit(editBuilder => {
      editBuilder.insert(editor.selection.active, text)
    })
  }

  async replaceText(range: IRange, text: string): Promise<void> {
    const editor = vscode.window.activeTextEditor
    if (!editor) {
      throw new Error('No active text editor')
    }
    
    const vsRange = new vscode.Range(
      range.start.line,
      range.start.character,
      range.end.line,
      range.end.character
    )
    
    await editor.edit(editBuilder => {
      editBuilder.replace(vsRange, text)
    })
  }

  onWorkspaceEvent(event: WorkspaceEvent, callback: (data: any) => void): IDisposable {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    
    this.eventListeners.get(event)!.push(callback)
    
    return {
      dispose: () => {
        const listeners = this.eventListeners.get(event)
        if (listeners) {
          const index = listeners.indexOf(callback)
          if (index >= 0) {
            listeners.splice(index, 1)
          }
        }
      }
    }
  }

  async showNotification(
    message: string, 
    type: NotificationType, 
    actions: string[] = []
  ): Promise<string | undefined> {
    switch (type) {
      case NotificationType.Info:
        return await vscode.window.showInformationMessage(message, ...actions)
      case NotificationType.Warning:
        return await vscode.window.showWarningMessage(message, ...actions)
      case NotificationType.Error:
        return await vscode.window.showErrorMessage(message, ...actions)
      default:
        return await vscode.window.showInformationMessage(message, ...actions)
    }
  }

  async showInputBox(options: InputBoxOptions): Promise<string | undefined> {
    return await vscode.window.showInputBox({
      prompt: options.prompt,
      placeHolder: options.placeholder,
      value: options.value,
      password: options.password,
      validateInput: options.validateInput
    })
  }

  async showQuickPick<T>(items: T[], options: QuickPickOptions<T>): Promise<T | undefined> {
    const quickPickItems = items.map(item => ({
      label: options.getLabel ? options.getLabel(item) : String(item),
      description: options.getDescription ? options.getDescription(item) : undefined,
      detail: options.getDetail ? options.getDetail(item) : undefined,
      item
    }))

    const selected = await vscode.window.showQuickPick(quickPickItems, {
      placeHolder: options.placeholder,
      canPickMany: options.canPickMany
    })

    if (Array.isArray(selected)) {
      return selected.map(s => s.item) as any
    }
    
    return selected?.item
  }

  private setupEventListeners(): void {
    // File opened/closed events
    this.disposables.push(
      vscode.window.onDidChangeActiveTextEditor(editor => {
        if (editor) {
          this.notifyListeners('fileOpened', { path: editor.document.uri.fsPath })
        }
      })
    )

    // Text selection changes
    this.disposables.push(
      vscode.window.onDidChangeTextEditorSelection(event => {
        this.notifyListeners('selectionChanged', {
          path: event.textEditor.document.uri.fsPath,
          selection: {
            start: {
              line: event.selections[0].start.line,
              character: event.selections[0].start.character
            },
            end: {
              line: event.selections[0].end.line,
              character: event.selections[0].end.character
            }
          }
        })
      })
    )

    // Workspace folder changes
    this.disposables.push(
      vscode.workspace.onDidChangeWorkspaceFolders(event => {
        event.added.forEach(folder => {
          this.notifyListeners('workspaceFolderAdded', {
            name: folder.name,
            path: folder.uri.fsPath
          })
        })
        
        event.removed.forEach(folder => {
          this.notifyListeners('workspaceFolderRemoved', {
            name: folder.name,
            path: folder.uri.fsPath
          })
        })
      })
    )

    // File changes
    this.disposables.push(
      vscode.workspace.onDidChangeTextDocument(event => {
        this.notifyListeners('fileChanged', {
          path: event.document.uri.fsPath,
          changes: event.contentChanges.length
        })
      })
    )
  }

  private notifyListeners(event: WorkspaceEvent, data: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(callback => callback(data))
    }
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose())
    this.disposables = []
    this.eventListeners.clear()
  }
}

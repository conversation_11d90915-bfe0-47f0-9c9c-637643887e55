import * as vscode from "vscode"
import * as fs from "fs/promises"
import * as path from "path"
import { IFileSystem, IFileStats, IDisposable } from "../interfaces/IFileSystem"

/**
 * VS Code implementation of the file system interface
 * Wraps VS Code file system APIs and Node.js fs for compatibility
 */
export class VSCodeFileSystem implements IFileSystem {
  async readFile(filePath: string): Promise<string> {
    try {
      // Try VS Code workspace fs first for virtual file systems
      if (vscode.workspace.fs) {
        const uri = vscode.Uri.file(filePath)
        const data = await vscode.workspace.fs.readFile(uri)
        return Buffer.from(data).toString('utf8')
      }
    } catch (error) {
      // Fall back to Node.js fs if VS Code fs fails
    }
    
    return await fs.readFile(filePath, 'utf8')
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const uri = vscode.Uri.file(filePath)
        const data = Buffer.from(content, 'utf8')
        await vscode.workspace.fs.writeFile(uri, data)
        return
      }
    } catch (error) {
      // Fall back to Node.js fs if VS Code fs fails
    }

    // Ensure directory exists
    await fs.mkdir(path.dirname(filePath), { recursive: true })
    await fs.writeFile(filePath, content, 'utf8')
  }

  async exists(filePath: string): Promise<boolean> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const uri = vscode.Uri.file(filePath)
        await vscode.workspace.fs.stat(uri)
        return true
      }
    } catch (error) {
      // Fall back to Node.js fs
    }

    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  async listFiles(dirPath: string, recursive: boolean = false): Promise<string[]> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const uri = vscode.Uri.file(dirPath)
        const entries = await vscode.workspace.fs.readDirectory(uri)
        const files: string[] = []
        
        for (const [name, type] of entries) {
          const fullPath = path.join(dirPath, name)
          
          if (type === vscode.FileType.File) {
            files.push(fullPath)
          } else if (type === vscode.FileType.Directory && recursive) {
            const subFiles = await this.listFiles(fullPath, recursive)
            files.push(...subFiles)
          }
        }
        
        return files
      }
    } catch (error) {
      // Fall back to Node.js fs
    }

    return await this.listFilesNodeJS(dirPath, recursive)
  }

  private async listFilesNodeJS(dirPath: string, recursive: boolean): Promise<string[]> {
    const entries = await fs.readdir(dirPath, { withFileTypes: true })
    const files: string[] = []

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name)
      
      if (entry.isFile()) {
        files.push(fullPath)
      } else if (entry.isDirectory() && recursive) {
        const subFiles = await this.listFilesNodeJS(fullPath, recursive)
        files.push(...subFiles)
      }
    }

    return files
  }

  watchFiles(
    pattern: string, 
    callback: (path: string, event: 'create' | 'change' | 'delete') => void
  ): IDisposable {
    // Use VS Code file system watcher
    const watcher = vscode.workspace.createFileSystemWatcher(pattern)
    
    const disposables = [
      watcher.onDidCreate(uri => callback(uri.fsPath, 'create')),
      watcher.onDidChange(uri => callback(uri.fsPath, 'change')),
      watcher.onDidDelete(uri => callback(uri.fsPath, 'delete')),
      watcher
    ]

    return {
      dispose: () => {
        disposables.forEach(d => d.dispose())
      }
    }
  }

  async stat(filePath: string): Promise<IFileStats> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const uri = vscode.Uri.file(filePath)
        const stat = await vscode.workspace.fs.stat(uri)
        
        return {
          isFile: () => stat.type === vscode.FileType.File,
          isDirectory: () => stat.type === vscode.FileType.Directory,
          size: stat.size,
          mtime: new Date(stat.mtime),
          ctime: new Date(stat.ctime)
        }
      }
    } catch (error) {
      // Fall back to Node.js fs
    }

    const stat = await fs.stat(filePath)
    return {
      isFile: () => stat.isFile(),
      isDirectory: () => stat.isDirectory(),
      size: stat.size,
      mtime: stat.mtime,
      ctime: stat.ctime
    }
  }

  async createDirectory(dirPath: string): Promise<void> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const uri = vscode.Uri.file(dirPath)
        await vscode.workspace.fs.createDirectory(uri)
        return
      }
    } catch (error) {
      // Fall back to Node.js fs
    }

    await fs.mkdir(dirPath, { recursive: true })
  }

  async delete(filePath: string, recursive: boolean = false): Promise<void> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const uri = vscode.Uri.file(filePath)
        await vscode.workspace.fs.delete(uri, { recursive, useTrash: false })
        return
      }
    } catch (error) {
      // Fall back to Node.js fs
    }

    const stat = await fs.stat(filePath)
    if (stat.isDirectory()) {
      await fs.rmdir(filePath, { recursive })
    } else {
      await fs.unlink(filePath)
    }
  }

  async copy(source: string, destination: string): Promise<void> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const sourceUri = vscode.Uri.file(source)
        const destUri = vscode.Uri.file(destination)
        await vscode.workspace.fs.copy(sourceUri, destUri, { overwrite: true })
        return
      }
    } catch (error) {
      // Fall back to Node.js fs
    }

    await fs.mkdir(path.dirname(destination), { recursive: true })
    await fs.copyFile(source, destination)
  }

  async move(source: string, destination: string): Promise<void> {
    try {
      // Try VS Code workspace fs first
      if (vscode.workspace.fs) {
        const sourceUri = vscode.Uri.file(source)
        const destUri = vscode.Uri.file(destination)
        await vscode.workspace.fs.rename(sourceUri, destUri, { overwrite: true })
        return
      }
    } catch (error) {
      // Fall back to Node.js fs
    }

    await fs.mkdir(path.dirname(destination), { recursive: true })
    await fs.rename(source, destination)
  }
}

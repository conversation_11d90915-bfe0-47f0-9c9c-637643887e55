import * as vscode from "vscode"
import { 
  IPlatformServices, 
  PlatformType, 
  PlatformInitOptions 
} from "../interfaces"
import { VSCodeFileSystem } from "./VSCodeFileSystem"
import { VSCodeWebviewProvider } from "./VSCodeWebview"
import { VSCodeConfiguration } from "./VSCodeConfiguration"
import { VSCodeLoggerFactory } from "./VSCodeLogger"
import { VSCodeWorkspace } from "./VSCodeWorkspace"

/**
 * VS Code implementation of platform services
 * Provides all platform-specific services for VS Code environment
 */
export class VSCodePlatformServices implements IPlatformServices {
  public readonly platformType = PlatformType.VSCode
  
  public readonly fileSystem: VSCodeFileSystem
  public readonly webview: VSCodeWebviewProvider
  public readonly configuration: VSCodeConfiguration
  public readonly logger: VSCodeLoggerFactory
  public readonly workspace: VSCodeWorkspace

  private context: vscode.ExtensionContext
  private initialized = false

  constructor(context: vscode.ExtensionContext) {
    this.context = context
    
    // Initialize services
    this.fileSystem = new VSCodeFileSystem()
    this.webview = new VSCodeWebviewProvider(context)
    this.configuration = new VSCodeConfiguration()
    this.logger = new VSCodeLoggerFactory()
    this.workspace = new VSCodeWorkspace()
  }

  async initialize(options?: PlatformInitOptions): Promise<void> {
    if (this.initialized) {
      return
    }

    // Set up global log level if specified
    if (options?.logLevel !== undefined) {
      this.logger.setGlobalLevel(options.logLevel)
    }

    // Register disposables with VS Code context
    this.context.subscriptions.push({
      dispose: () => this.dispose()
    })

    this.initialized = true
  }

  async dispose(): Promise<void> {
    if (!this.initialized) {
      return
    }

    // Dispose all services
    this.configuration.dispose()
    this.logger.dispose()
    this.workspace.dispose()
    this.webview.dispose()

    this.initialized = false
  }

  /**
   * Get the VS Code extension context
   */
  getExtensionContext(): vscode.ExtensionContext {
    return this.context
  }

  /**
   * Create a logger for a specific component
   */
  createLogger(name: string) {
    return this.logger.createLogger(name)
  }

  /**
   * Get the current workspace root path
   */
  getWorkspaceRoot(): string | undefined {
    return this.workspace.getRootPath()
  }

  /**
   * Check if running in development mode
   */
  isDevelopmentMode(): boolean {
    return this.context.extensionMode === vscode.ExtensionMode.Development
  }

  /**
   * Get extension version
   */
  getExtensionVersion(): string {
    return this.context.extension.packageJSON.version
  }

  /**
   * Get extension path
   */
  getExtensionPath(): string {
    return this.context.extensionPath
  }

  /**
   * Register a command with VS Code
   */
  registerCommand(command: string, callback: (...args: any[]) => any): vscode.Disposable {
    const disposable = vscode.commands.registerCommand(command, callback)
    this.context.subscriptions.push(disposable)
    return disposable
  }

  /**
   * Execute a VS Code command
   */
  async executeCommand(command: string, ...args: any[]): Promise<any> {
    return await vscode.commands.executeCommand(command, ...args)
  }

  /**
   * Get global state value
   */
  getGlobalState<T>(key: string): T | undefined {
    return this.context.globalState.get<T>(key)
  }

  /**
   * Set global state value
   */
  async setGlobalState<T>(key: string, value: T): Promise<void> {
    await this.context.globalState.update(key, value)
  }

  /**
   * Get workspace state value
   */
  getWorkspaceState<T>(key: string): T | undefined {
    return this.context.workspaceState.get<T>(key)
  }

  /**
   * Set workspace state value
   */
  async setWorkspaceState<T>(key: string, value: T): Promise<void> {
    await this.context.workspaceState.update(key, value)
  }

  /**
   * Get secret value
   */
  async getSecret(key: string): Promise<string | undefined> {
    return await this.context.secrets.get(key)
  }

  /**
   * Set secret value
   */
  async setSecret(key: string, value: string): Promise<void> {
    await this.context.secrets.store(key, value)
  }

  /**
   * Delete secret value
   */
  async deleteSecret(key: string): Promise<void> {
    await this.context.secrets.delete(key)
  }
}

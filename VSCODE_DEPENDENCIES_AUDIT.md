# VS Code Dependencies Audit

## Executive Summary

The roo-code project is heavily integrated with VS Code APIs across multiple layers. This audit identifies 14 core files with direct VS Code imports and numerous indirect dependencies through the extension architecture.

## Direct VS Code API Dependencies

### 1. Extension Infrastructure Files

#### `src/extension.ts` - Main Extension Entry Point
**VS Code APIs Used:**
- `vscode.ExtensionContext` - Extension lifecycle management
- `vscode.window.createOutputChannel()` - Logging
- `vscode.workspace.getConfiguration()` - Settings access
- `vscode.workspace.createFileSystemWatcher()` - File watching
- `vscode.commands.executeCommand()` - Command execution
- `vscode.window.registerWebviewViewProvider()` - Webview registration
- `vscode.workspace.registerTextDocumentContentProvider()` - Virtual documents
- `vscode.window.registerUriHandler()` - URI handling
- `vscode.languages.registerCodeActionsProvider()` - Code actions

#### `src/package.json` - Extension Manifest
**VS Code Specific Configuration:**
- Extension metadata and activation events
- Command definitions and menu contributions
- Webview view containers and views
- Configuration schema for settings
- Keybinding and context menu definitions

### 2. Webview and UI Integration

#### `src/core/webview/ClineProvider.ts` - Main Webview Provider
**VS Code APIs Used:**
- `vscode.WebviewViewProvider` - Interface implementation
- `vscode.WebviewView` / `vscode.WebviewPanel` - UI containers
- `vscode.Webview` - Content and messaging
- `vscode.ExtensionMode` - Development mode detection
- `vscode.workspace.onDidChangeConfiguration()` - Theme changes
- `vscode.ThemeIcon` - Icon handling

#### `webview-ui/src/utils/vscode.ts` - VS Code API Wrapper
**VS Code APIs Used:**
- `acquireVsCodeApi()` - Webview communication
- `WebviewApi` - Message passing and state management

### 3. Terminal Integration (To Be Removed)

#### `src/integrations/terminal/Terminal.ts` - VS Code Terminal Wrapper
**VS Code APIs Used:**
- `vscode.Terminal` - Terminal instance management
- `vscode.window.createTerminal()` - Terminal creation
- `vscode.ThemeIcon` - Terminal icons
- `terminal.shellIntegration` - Shell integration APIs

#### `src/integrations/terminal/TerminalRegistry.ts` - Terminal Management
**VS Code APIs Used:**
- `vscode.window.onDidCloseTerminal()` - Terminal lifecycle events
- `vscode.Disposable` - Resource cleanup

### 4. Editor and File Operations

#### `src/integrations/editor/DiffViewProvider.ts` - Diff View Management
**VS Code APIs Used:**
- `vscode.TextEditor` - Editor instances
- `vscode.workspace.textDocuments` - Document access
- `vscode.window.onDidChangeActiveTextEditor()` - Editor events
- `vscode.commands.executeCommand("vscode.diff")` - Diff command
- `vscode.Uri` - File URI handling
- `vscode.ViewColumn` - Editor layout

#### `src/integrations/editor/EditorUtils.ts` - Editor Utilities
**VS Code APIs Used:**
- `vscode.window.activeTextEditor` - Current editor
- `vscode.Selection` - Text selection
- `vscode.Range` - Text ranges

### 5. Command and Action Registration

#### `src/activate/registerCommands.ts` - Command Registration
**VS Code APIs Used:**
- `vscode.commands.registerCommand()` - Command registration
- `vscode.window.visibleTextEditors` - Editor state
- `vscode.window.createWebviewPanel()` - Panel creation
- `vscode.ViewColumn` - Layout management

#### `src/activate/registerCodeActions.ts` - Code Action Registration
**VS Code APIs Used:**
- `vscode.commands.registerCommand()` - Action registration
- `vscode.ExtensionContext` - Context management

#### `src/activate/registerTerminalActions.ts` - Terminal Action Registration
**VS Code APIs Used:**
- `vscode.commands.registerCommand()` - Terminal command registration
- `vscode.window.showWarningMessage()` - User notifications

### 6. AI Provider Integration

#### `src/api/providers/vscode-lm.ts` - VS Code Language Model Integration
**VS Code APIs Used:**
- `vscode.lm.selectChatModels()` - Model selection
- `vscode.LanguageModelChat` - Chat interface
- `vscode.LanguageModelChatMessage` - Message types
- `vscode.CancellationTokenSource` - Request cancellation
- `vscode.workspace.onDidChangeConfiguration()` - Configuration changes

### 7. File System and Workspace Operations

#### `src/services/code-index/processors/file-watcher.ts` - File Watching
**VS Code APIs Used:**
- `vscode.workspace.createFileSystemWatcher()` - File system monitoring
- `vscode.RelativePattern` - Pattern matching

#### `src/services/ripgrep/index.ts` - File Search
**VS Code APIs Used:**
- `vscode.env.appRoot` - VS Code installation path (for ripgrep binary)

### 8. Configuration and Settings

#### `src/core/webview/webviewMessageHandler.ts` - Settings Management
**VS Code APIs Used:**
- `vscode.workspace.getConfiguration()` - Settings access
- `vscode.window.showErrorMessage()` - Error notifications

#### `src/core/config/CustomModesManager.ts` - Custom Modes
**VS Code APIs Used:**
- `vscode.workspace.workspaceFolders` - Workspace access

### 9. Testing Infrastructure

#### `apps/vscode-e2e/src/suite/index.ts` - E2E Tests
**VS Code APIs Used:**
- `vscode.extensions.getExtension()` - Extension access
- Extension activation and API testing

## Indirect Dependencies

### 1. Extension Context Usage
- State management across multiple services
- Resource cleanup and disposal
- Subscription management for events

### 2. VS Code Types and Interfaces
- `ExtensionContext` used in 15+ files
- `OutputChannel` for logging throughout
- `Disposable` for resource management
- `Uri` for file path handling

### 3. VS Code Specific Utilities
- Path handling utilities that assume VS Code environment
- Theme detection and management
- Icon and asset management

## Terminal System Dependencies (For Removal)

### Core Terminal Files:
- `src/integrations/terminal/Terminal.ts`
- `src/integrations/terminal/TerminalRegistry.ts`
- `src/integrations/terminal/TerminalProcess.ts`
- `src/integrations/terminal/ExecaTerminal.ts`
- `src/integrations/terminal/ExecaTerminalProcess.ts`
- `src/integrations/terminal/BaseTerminal.ts`
- `src/integrations/terminal/BaseTerminalProcess.ts`
- `src/integrations/terminal/ShellIntegrationManager.ts`
- `src/integrations/terminal/types.ts`

### Terminal-Related Commands:
- `roo-cline.terminalAddToContext`
- `roo-cline.terminalFixCommand`
- `roo-cline.terminalExplainCommand`

### Terminal UI Components:
- Terminal menu contributions in package.json
- Terminal context menu items
- Terminal-related webview messages

## Configuration Dependencies

### VS Code Settings Schema:
- `roo-cline.allowedCommands`
- `roo-cline.vsCodeLmModelSelector`
- `roo-cline.customStoragePath`
- `roo-cline.rooCodeCloudEnabled`

### Extension Activation:
- `onLanguage` and `onStartupFinished` activation events
- Command palette integration
- Activity bar and sidebar integration

## Webview Communication Dependencies

### Message Types:
- Extension to webview communication
- Webview to extension communication
- State synchronization
- Command execution requests

### Content Security Policy:
- VS Code specific CSP requirements
- Resource URI handling
- Script and style loading

## Recommendations for Refactoring

### High Priority (Critical Path):
1. **Abstract Extension Context** - Create platform-agnostic context management
2. **Replace Webview Provider** - Implement HTTP/WebSocket server
3. **Remove Terminal System** - Complete removal of terminal functionality
4. **Abstract Configuration** - File-based configuration system

### Medium Priority:
1. **Abstract File Operations** - Platform-agnostic file system interface
2. **Replace Command System** - HTTP API endpoints
3. **Abstract Logging** - File-based logging system

### Low Priority:
1. **Theme Management** - Simplified theme system
2. **Icon Management** - Web-compatible icon system
3. **URI Handling** - Standard URL/path handling

## Migration Strategy

### Phase 1: Create Abstractions
- Define platform interfaces
- Implement VS Code adapters
- Prepare for dual-mode operation

### Phase 2: Remove Dependencies
- Eliminate terminal system
- Replace webview with web server
- Implement standalone configuration

### Phase 3: Standalone Implementation
- Create standalone platform implementations
- Build web-based UI
- Implement HTTP/WebSocket APIs

### Phase 4: Testing and Validation
- Comprehensive testing of standalone mode
- Performance validation
- User experience testing

This audit provides the foundation for the systematic removal of VS Code dependencies while maintaining core functionality in a standalone application.

{"greeting": "¡Hola, soy Roo!", "introduction": "<strong>Roo Code es el principal agente de codificación autónomo.</strong> Prepárate para arquitectar, codificar, depurar y aumentar tu productividad como nunca antes. Para continuar, Roo Code requiere una clave API.", "notice": "Para comenzar, esta extensión necesita un proveedor de API.", "start": "¡Vamos!", "chooseProvider": "Elige un proveedor de API para comenzar:", "routers": {"requesty": {"description": "Tu router LLM optimizado", "incentive": "$1 de crédito gratis"}, "openrouter": {"description": "Una interfaz unificada para LLMs"}}, "startRouter": "Configuración rápida a través de un router", "startCustom": "Usa tu propia clave API", "telemetry": {"title": "<PERSON><PERSON><PERSON> a mejorar Roo <PERSON>", "anonymousTelemetry": "Envía datos de uso y errores anónimos para ayudarnos a corregir errores y mejorar la extensión. Nunca se envía código, texto o información personal.", "changeSettings": "Siempre puedes cambiar esto en la parte inferior de la <settingsLink>configuración</settingsLink>", "settings": "configuración", "allow": "<PERSON><PERSON><PERSON>", "deny": "<PERSON><PERSON><PERSON>"}, "or": "o", "importSettings": "Importar configuración"}
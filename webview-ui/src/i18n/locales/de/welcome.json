{"greeting": "<PERSON><PERSON>, ich bin Roo!", "introduction": "<strong>Roo Code ist der führende autonome Coding-Agent.</strong> <PERSON><PERSON> dich bereit, zu architektieren, zu coden, zu debuggen und deine Produktivität wie nie zuvor zu steigern. Um fortzufahren, benötigt Roo Code einen API-Schlüssel.", "notice": "Um loszulegen, ben<PERSON><PERSON>gt diese Erweiterung einen API-Anbieter.", "start": "Los geht's!", "chooseProvider": "Wähle einen API-Anbieter, um zu beginnen:", "routers": {"requesty": {"description": "<PERSON>in optimierter LLM-Router", "incentive": "$1 Guthaben gratis"}, "openrouter": {"description": "Eine einheitliche Schnittstelle für LLMs"}}, "startRouter": "Express-Einrichtung über einen Router", "startCustom": "Eigenen API-Schlüssel verwenden", "telemetry": {"title": "<PERSON><PERSON>, <PERSON><PERSON> <PERSON> zu verbessern", "anonymousTelemetry": "Sende anonyme Fehler- und Nutzungsdaten, um uns bei der Fehlerbehebung und Verbesserung der Erweiterung zu helfen. Es werden niemals Code, Texte oder persönliche Informationen gesendet.", "changeSettings": "Du kannst dies jederzeit unten in den <settingsLink>Einstellungen</settingsLink> ändern", "settings": "Einstellungen", "allow": "Erlauben", "deny": "<PERSON><PERSON><PERSON><PERSON>"}, "or": "oder", "importSettings": "Einstellungen importieren"}
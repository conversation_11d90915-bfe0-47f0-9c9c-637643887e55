{"recentTasks": "Tasks", "viewAll": "View All Tasks", "tokens": "Tokens: ↑{{in}} ↓{{out}}", "cache": "Cache: +{{writes}} → {{reads}}", "apiCost": "API Cost: ${{cost}}", "history": "History", "exitSelectionMode": "Exit Selection Mode", "enterSelectionMode": "Enter Selection Mode", "done": "Done", "searchPlaceholder": "Fuzzy search history...", "newest": "Newest", "oldest": "Oldest", "mostExpensive": "Most Expensive", "mostTokens": "Most Tokens", "mostRelevant": "Most Relevant", "deleteTaskTitle": "Delete Task (<PERSON><PERSON> + <PERSON>lick to skip confirmation)", "tokensLabel": "Tokens:", "cacheLabel": "Cache:", "apiCostLabel": "API Cost:", "copyPrompt": "Copy Prompt", "exportTask": "Export Task", "deleteTask": "Delete Task", "deleteTaskMessage": "Are you sure you want to delete this task? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "exitSelection": "Exit Selection", "selectionMode": "Selection Mode", "deselectAll": "Deselect all", "selectAll": "Select all", "selectedItems": "Selected {{selected}}/{{total}} items", "clearSelection": "Clear Selection", "deleteSelected": "Delete Selected", "deleteTasks": "Delete Tasks", "confirmDeleteTasks": "Are you sure you want to delete {{count}} tasks?", "deleteTasksWarning": "Deleted tasks cannot be recovered. Please make sure you want to proceed.", "deleteItems": "Delete {{count}} Items", "showAllWorkspaces": "Show tasks from all workspaces"}
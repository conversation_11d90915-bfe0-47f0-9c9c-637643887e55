{"title": "MCP Servers", "done": "Done", "description": "The <0>Model Context Protocol</0> enables communication with locally running MCP servers that provide additional tools and resources to extend Roo's capabilities. You can use <1>community-made servers</1> or ask R<PERSON> to create new tools specific to your workflow (e.g., \"add a tool that gets the latest npm docs\").", "instructions": "Instructions", "enableToggle": {"title": "Enable MCP Servers", "description": "Turn this ON to let Roo use tools from connected MCP servers. This gives Roo more capabilities. If you don't plan to use these extra tools, turn it OFF to help reduce API token costs."}, "enableServerCreation": {"title": "Enable MCP Server Creation", "description": "Enable this to have <PERSON><PERSON> help you build <1>new</1> custom MCP servers. <0>Learn about server creation</0>", "hint": "Hint: To reduce API token costs, disable this setting when you are not actively asking R<PERSON> to create a new MCP server."}, "editGlobalMCP": "Edit Global MCP", "editProjectMCP": "Edit Project MCP", "learnMoreEditingSettings": "Learn more about editing MCP settings files", "tool": {"alwaysAllow": "Always allow", "parameters": "Parameters", "noDescription": "No description"}, "tabs": {"tools": "Tools", "resources": "Resources", "errors": "Errors"}, "emptyState": {"noTools": "No tools found", "noResources": "No resources found", "noLogs": "No logs found", "noErrors": "No errors found"}, "networkTimeout": {"label": "Network Timeout", "description": "Maximum time to wait for server responses", "options": {"15seconds": "15 seconds", "30seconds": "30 seconds", "1minute": "1 minute", "5minutes": "5 minutes", "10minutes": "10 minutes", "15minutes": "15 minutes", "30minutes": "30 minutes", "60minutes": "60 minutes"}}, "deleteDialog": {"title": "Delete MCP Server", "description": "Are you sure you want to delete the MCP server \"{{serverName}}\"? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "serverStatus": {"retrying": "Retrying...", "retryConnection": "Retry Connection"}}
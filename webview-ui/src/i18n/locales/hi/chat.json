{"greeting": "Roo Code में आपका स्वागत है", "task": {"title": "कार्य", "seeMore": "अधिक देखें", "seeLess": "कम देखें", "tokens": "Tokens:", "cache": "कैश:", "apiCost": "API लागत:", "contextWindow": "संदर्भ लंबाई:", "closeAndStart": "कार्य बंद करें और नया शुरू करें", "export": "कार्य इतिहास निर्यात करें", "delete": "कार्य हटाएं (पुष्टि को छोड़ने के लिए Shift + क्लिक)", "condenseContext": "संदर्भ को बुद्धिमानी से संघनित करें"}, "unpin": "पिन करें", "pin": "अवपिन करें", "tokenProgress": {"availableSpace": "उपलब्ध स्थान: {{amount}} tokens", "tokensUsed": "प्रयुक्त tokens: {{used}} / {{total}}", "reservedForResponse": "मॉडल प्रतिक्रिया के लिए आरक्षित: {{amount}} tokens"}, "retry": {"title": "पुनः प्रयास करें", "tooltip": "ऑपरेशन फिर से प्रयास करें"}, "startNewTask": {"title": "नया कार्य शुरू करें", "tooltip": "नया कार्य शुरू करें"}, "proceedAnyways": {"title": "फिर भी आगे बढ़ें", "tooltip": "कमांड निष्पादन के दौरान जारी रखें"}, "save": {"title": "सहेजें", "tooltip": "फ़ाइल परिवर्तन सहेजें"}, "reject": {"title": "अस्वीकार करें", "tooltip": "इस क्रिया को अस्वीकार करें"}, "completeSubtaskAndReturn": "उपकार्य पूरा करें और वापस लौटें", "approve": {"title": "स्वीकृत करें", "tooltip": "इस क्रिया को स्वीकृत करें"}, "runCommand": {"title": "कमांड चलाएँ", "tooltip": "इस कमांड को निष्पादित करें"}, "proceedWhileRunning": {"title": "चलते समय आगे बढ़ें", "tooltip": "चेतावनियों के बावजूद जारी रखें"}, "killCommand": {"title": "कमांड रोकें", "tooltip": "वर्तमान कमांड रोकें"}, "resumeTask": {"title": "कार्य जारी रखें", "tooltip": "वर्तमान कार्य जारी रखें"}, "terminate": {"title": "समाप्त करें", "tooltip": "वर्तमान कार्य समाप्त करें"}, "cancel": {"title": "रद्<PERSON> करें", "tooltip": "वर्तमान ऑपरेशन रद्द करें"}, "scrollToBottom": "चैट के निचले हिस्से तक स्क्रॉल करें", "about": "एआई सहायता से कोड जेनरेट करें, रिफैक्टर करें और डिबग करें। अधिक जानने के लिए हमारे <DocsLink>दस्तावेज़</DocsLink> देखें।", "onboarding": "एजेंटिक कोडिंग क्षमताओं में नवीनतम प्रगति के कारण, मैं जटिल सॉफ्टवेयर विकास कार्यों को चरण-दर-चरण संभाल सकता हूं। ऐसे उपकरणों के साथ जो मुझे फ़ाइलें बनाने और संपादित करने, जटिल प्रोजेक्ट का अन्वेषण करने, ब्राउज़र का उपयोग करने और टर्मिनल कमांड (आपकी अनुमति के बाद) निष्पादित करने की अनुमति देते हैं, मैं आपकी मदद कोड पूर्णता या तकनीकी समर्थन से परे तरीकों से कर सकता हूं। मैं अपनी क्षमताओं का विस्तार करने और नए उपकरण बनाने के लिए MCP का भी उपयोग कर सकता हूं।", "rooTips": {"boomerangTasks": {"title": "बूमरैंग कार्य", "description": "कार्यों को छोटे, प्रबंधनीय भागों में विभाजित करें।"}, "stickyModels": {"title": "स्टिकी मोड", "description": "प्रत्येक मोड आपके अंतिम उपयोग किए गए मॉडल को याद रखता है"}, "tools": {"title": "उपकरण", "description": "एआई को वेब ब्राउज़ करके, कमांड चलाकर और अधिक समस्याओं को हल करने की अनुमति दें।"}, "customizableModes": {"title": "अनुकूलन योग्य मोड", "description": "विशिष्ट प्रोफाइल अपने व्यवहार और निर्धारित मॉडल के साथ"}}, "selectMode": "इंटरैक्शन मोड चुनें", "selectApiConfig": "एपीआई कॉन्फ़िगरेशन का चयन करें", "enhancePrompt": "अतिरिक्त संदर्भ के साथ प्रॉम्प्ट बढ़ाएँ", "addImages": "संदेश में चित्र जोड़ें", "sendMessage": "संदेश भेजें", "typeMessage": "एक संदेश लिखें...", "typeTask": "अपना कार्य यहां लिखें...", "addContext": "संदर्भ जोड़ने के लिए @, मोड बदलने के लिए /", "dragFiles": "फ़ाइलें खींचने के लिए shift दबाकर रखें", "dragFilesImages": "फ़ाइलें/चित्र खींचने के लिए shift दबाकर रखें", "enhancePromptDescription": "'प्रॉम्प्ट बढ़ाएँ' बटन अतिरिक्त संदर्भ, स्पष्टीकरण या पुनर्विचार प्रदान करके आपके अनुरोध को बेहतर बनाने में मदद करता है। यहां अनुरोध लिखकर देखें और यह कैसे काम करता है यह देखने के लिए बटन पर फिर से क्लिक करें।", "errorReadingFile": "फ़ाइल पढ़ने में त्रुटि:", "noValidImages": "कोई मान्य चित्र प्रोसेस नहीं किया गया", "separator": "विभाजक", "edit": "संपादित करें...", "forNextMode": "अगले मोड के लिए", "error": "त्रुटि", "diffError": {"title": "संपादन असफल"}, "troubleMessage": "<PERSON>oo को समस्या हो रही है...", "apiRequest": {"title": "API अनुरोध", "failed": "API अनुरोध विफल हुआ", "streaming": "API अनुरोध...", "cancelled": "API अनुरोध रद्द किया गया", "streamingFailed": "API स्ट्रीमिंग विफल हुई"}, "checkpoint": {"initial": "प्रारंभिक चेकपॉइंट", "regular": "चेकपॉइंट", "initializingWarning": "चेकपॉइंट अभी भी आरंभ हो रहा है... अगर यह बहुत समय ले रहा है, तो आप <settingsLink>सेटिंग्स</settingsLink> में चेकपॉइंट को अक्षम कर सकते हैं और अपने कार्य को पुनः आरंभ कर सकते हैं।", "menu": {"viewDiff": "अंतर देखें", "restore": "चेकपॉइंट पुनर्स्थापित करें", "restoreFiles": "फ़ाइलें पुनर्स्थापित करें", "restoreFilesDescription": "आपके प्रोजेक्ट की फ़ाइलों को इस बिंदु पर लिए गए स्नैपशॉट पर पुनर्स्थापित करता है।", "restoreFilesAndTask": "फ़ाइलें और कार्य पुनर्स्थापित करें", "confirm": "पुष्टि करें", "cancel": "रद्<PERSON> करें", "cannotUndo": "इस क्रिया को पूर्ववत नहीं किया जा सकता।", "restoreFilesAndTaskDescription": "आपके प्रोजेक्ट की फ़ाइलों को इस बिंदु पर लिए गए स्नैपशॉट पर पुनर्स्थापित करता है और इस बिंदु के बाद के सभी संदेशों को हटा देता है।"}, "current": "वर्तमान"}, "instructions": {"wantsToFetch": "<PERSON>oo को वर्तमान कार्य में सहायता के लिए विस्तृत निर्देश प्राप्त करना है"}, "fileOperations": {"wantsToRead": "Roo इस फ़ाइल को पढ़ना चाहता है:", "wantsToReadOutsideWorkspace": "<PERSON>oo कार्यक्षेत्र के बाहर इस फ़ाइल को पढ़ना चाहता है:", "didRead": "<PERSON>oo ने इस फ़ाइल को पढ़ा:", "wantsToEdit": "<PERSON>oo इस फ़ाइल को संपादित करना चाहता है:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON> कार्यक्षेत्र के बाहर इस फ़ाइल को संपादित करना चाहता है:", "wantsToCreate": "Roo एक नई फ़ाइल बनाना चाहता है:", "wantsToSearchReplace": "Roo इस फ़ाइल में खोज और प्रतिस्थापन करना चाहता है:", "didSearchReplace": "<PERSON>oo ने इस फ़ाइल में खोज और प्रतिस्थापन किया:", "wantsToInsert": "Roo इस फ़ाइल में सामग्री डालना चाहता है:", "wantsToInsertWithLineNumber": "<PERSON>oo इस फ़ाइल की {{lineNumber}} लाइन पर सामग्री डालना चाहता है:", "wantsToInsertAtEnd": "Roo इस फ़ाइल के अंत में सामग्री जोड़ना चाहता है:", "wantsToReadAndXMore": "रू इस फ़ाइल को और {{count}} अन्य को पढ़ना चाहता है:", "wantsToReadMultiple": "Roo कई फ़ाइलें पढ़ना चाहता है:"}, "directoryOperations": {"wantsToViewTopLevel": "Roo इस निर्देशिका में शीर्ष स्तर की फ़ाइलें देखना चाहता है:", "didViewTopLevel": "Roo ने इस निर्देशिका में शीर्ष स्तर की फ़ाइलें देखीं:", "wantsToViewRecursive": "Roo इस निर्देशिका में सभी फ़ाइलों को पुनरावर्ती रूप से देखना चाहता है:", "didViewRecursive": "Roo ने इस निर्देशिका में सभी फ़ाइलों को पुनरावर्ती रूप से देखा:", "wantsToViewDefinitions": "Roo इस निर्देशिका में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखना चाहता है:", "didViewDefinitions": "Roo ने इस निर्देशिका में उपयोग किए गए सोर्स कोड परिभाषा नामों को देखा:", "wantsToSearch": "Roo इस निर्देशिका में <code>{{regex}}</code> के लिए खोज करना चाहता है:", "didSearch": "<PERSON>oo ने इस निर्देशिका में <code>{{regex}}</code> के लिए खोज की:"}, "commandOutput": "कमांड आउटपुट", "response": "प्रतिक्रिया", "arguments": "आर्ग्युमेंट्स", "mcp": {"wantsToUseTool": "<PERSON>oo {{serverName}} MCP सर्वर पर एक टूल का उपयोग करना चाहता है:", "wantsToAccessResource": "<PERSON>oo {{serverName}} MCP सर्वर पर एक संसाधन का उपयोग करना चाहता है:"}, "modes": {"wantsToSwitch": "Roo <code>{{mode}}</code> मोड में स्विच करना चाहता है", "wantsToSwitchWithReason": "Roo <code>{{mode}}</code> मोड में स्विच करना चाहता है क्योंकि: {{reason}}", "didSwitch": "Roo <code>{{mode}}</code> मोड में स्विच कर गया", "didSwitchWithReason": "<PERSON>oo <code>{{mode}}</code> मोड में स्विच कर गया क्योंकि: {{reason}}"}, "subtasks": {"wantsToCreate": "Roo <code>{{mode}}</code> मोड में एक नया उपकार्य बनाना चाहता है:", "wantsToFinish": "<PERSON>oo इस उपकार्य को समाप्त करना चाहता है", "newTaskContent": "उपकार्य निर्देश", "completionContent": "उपकार्य पूर्ण", "resultContent": "उपकार्य परिणाम", "defaultResult": "कृपया अगले कार्य पर जारी रखें।", "completionInstructions": "उपकार्य पूर्ण! आप परिणामों की समीक्षा कर सकते हैं और सुधार या अगले चरण सुझा सकते हैं। यदि सब कुछ ठीक लगता है, तो मुख्य कार्य को परिणाम वापस करने के लिए पुष्टि करें।"}, "questions": {"hasQuestion": "Roo का एक प्रश्न है:"}, "taskCompleted": "कार्य पूरा हुआ", "powershell": {"issues": "ऐसा लगता है कि आपको Windows PowerShell के साथ समस्याएँ हो रही हैं, कृपया इसे देखें"}, "autoApprove": {"title": "स्वत:-स्वीकृति:", "none": "कोई नहीं", "description": "स्वत:-स्वीकृति Roo Code को अनुमति मांगे बिना क्रियाएँ करने की अनुमति देती है। केवल उन क्रियाओं के लिए सक्षम करें जिन पर आप पूरी तरह से विश्वास करते हैं। अधिक विस्तृत कॉन्फ़िगरेशन <settingsLink>सेटिंग्स</settingsLink> में उपलब्ध है।"}, "reasoning": {"thinking": "विचार कर रहा है", "seconds": "{{count}} सेकंड"}, "contextCondense": {"title": "संदर्भ संक्षिप्त किया गया", "condensing": "संदर्भ संघनित कर रहा है...", "errorHeader": "संदर्भ संघनित करने में विफल", "tokens": "टोकन"}, "followUpSuggest": {"copyToInput": "इनपुट में कॉपी करें (या Shift + क्लिक)"}, "announcement": {"title": "🎉 Roo Code {{version}} रिलीज़ हुआ", "description": "Roo Code {{version}} आपके फीडबैक के आधार पर शक्तिशाली नई सुविधाएँ और सुधार लाता है।", "whatsNew": "नई सुविधाएँ", "feature1": "<bold>बुद्धिमान संदर्भ संघनन डिफ़ॉल्ट रूप से सक्षम</bold>: संदर्भ संघनन अब डिफ़ॉल्ट रूप से सक्षम है और स्वचालित संघनन कब होगा इसके लिए कॉन्फ़िगरेबल सेटिंग्स हैं", "feature2": "<bold>मैन्युअल संघनन बटन</bold>: कार्य हेडर में नया बटन आपको किसी भी समय मैन्युअल रूप से संदर्भ संघनन ट्रिगर करने की अनुमति देता है", "feature3": "<bold>उन्नत संघनन सेटिंग्स</bold>: <contextSettingsLink>संदर्भ सेटिंग्स</contextSettingsLink> के माध्यम से स्वचालित संघनन कब और कैसे होता है इसे फाइन-ट्यून करें", "hideButton": "घोषणा छिपाएँ", "detailsDiscussLinks": "<discordLink>Discord</discordLink> और <redditLink>Reddit</redditLink> पर अधिक जानकारी प्राप्त करें और चर्चा में भाग लें 🚀"}, "browser": {"rooWantsToUse": "Roo ब्राउज़र का उपयोग करना चाहता है:", "consoleLogs": "कंसोल लॉग", "noNewLogs": "(कोई नया लॉग नहीं)", "screenshot": "ब्राउज़र स्क्रीनशॉट", "cursor": "कर्सर", "navigation": {"step": "चरण {{current}} / {{total}}", "previous": "पिछला", "next": "अगला"}, "sessionStarted": "ब्राउज़र सत्र शुरू हुआ", "actions": {"title": "ब्राउज़र क्रिया: ", "launch": "{{url}} पर ब्राउज़र लॉन्च करें", "click": "क्लिक करें ({{coordinate}})", "type": "टाइप करें \"{{text}}\"", "scrollDown": "नीचे स्क्रॉल करें", "scrollUp": "ऊपर स्क्रॉल करें", "close": "ब्राउज़र बंद करें"}}, "codeblock": {"tooltips": {"expand": "कोड ब्लॉक का विस्तार करें", "collapse": "कोड ब्लॉक को संकुचित करें", "enable_wrap": "वर्ड रैप सक्षम करें", "disable_wrap": "वर्ड रैप अक्षम करें", "copy_code": "कोड कॉपी करें"}}, "systemPromptWarning": "चेतावनी: कस्टम सिस्टम प्रॉम्प्ट ओवरराइड सक्रिय है। यह कार्यक्षमता को गंभीर रूप से बाधित कर सकता है और अनियमित व्यवहार का कारण बन सकता है.", "profileViolationWarning": "वर्तमान प्रोफ़ाइल आपके संगठन की सेटिंग्स का उल्लंघन करती है", "shellIntegration": {"title": "कमांड निष्पादन चेतावनी", "description": "आपका कमांड VSCode टर्मिनल शेल इंटीग्रेशन के बिना निष्पादित हो रहा है। इस चेतावनी को दबाने के लिए आप <settingsLink>Roo Code सेटिंग्स</settingsLink> के <strong>Terminal</strong> अनुभाग में शेल इंटीग्रेशन को अक्षम कर सकते हैं या नीचे दिए गए लिंक का उपयोग करके VSCode टर्मिनल इंटीग्रेशन की समस्या का समाधान कर सकते हैं।", "troubleshooting": "शेल इंटीग्रेशन दस्तावेज़ के लिए यहां क्लिक करें।"}, "ask": {"autoApprovedRequestLimitReached": {"title": "स्वत:-स्वीकृत अनुरोध सीमा पहुंची", "description": "Roo {{count}} API अनुरोध(धों) की स्वत:-स्वीकृत सीमा तक पहुंच गया है। क्या आप गणना को रीसेट करके कार्य जारी रखना चाहते हैं?", "button": "रीसेट करें और जारी रखें"}}, "codebaseSearch": {"wantsToSearch": "<PERSON>oo कोडबेस में <code>{{query}}</code> खोजना चाहता है:", "wantsToSearchWithPath": "Roo <code>{{path}}</code> में कोडबेस में <code>{{query}}</code> खोजना चाहता है:", "didSearch": "<code>{{query}}</code> के लिए {{count}} परिणाम मिले:"}, "read-batch": {"approve": {"title": "सभी स्वीकृत करें"}, "deny": {"title": "सभी अस्वीकार करें"}}}
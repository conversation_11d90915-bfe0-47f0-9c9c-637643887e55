{"greeting": "안녕하세요, 저는 Roo입니다!", "introduction": "<strong><PERSON><PERSON> Code는 최고의 자율적 코딩 에이전트입니다.</strong> 아키텍처 설계, 코딩, 디버깅, 그리고 전례 없는 생산성 향상을 경험할 준비를 하세요. 계속하려면 Roo Code에 API 키가 필요합니다.", "notice": "시작하려면 이 확장 프로그램에 API 공급자가 필요합니다.", "start": "시작해 봅시다!", "chooseProvider": "시작하려면 API 공급자를 선택하세요:", "routers": {"requesty": {"description": "최적화된 LLM 라우터", "incentive": "$1 무료 크레딧"}, "openrouter": {"description": "LLM을 위한 통합 인터페이스"}}, "startRouter": "라우터를 통한 빠른 설정", "startCustom": "직접 API 키 사용하기", "telemetry": {"title": "Roo Code 개선에 도움 주세요", "anonymousTelemetry": "버그 수정 및 확장 기능 개선을 위해 익명의 오류 및 사용 데이터를 보내주세요. 코드, 프롬프트 또는 개인 정보는 절대 전송되지 않습니다.", "changeSettings": "<settingsLink>설정</settingsLink> 하단에서 언제든지 변경할 수 있습니다", "settings": "설정", "allow": "허용", "deny": "거부"}, "or": "또는", "importSettings": "설정 가져오기"}
{"greeting": "Привет, я Roo!", "introduction": "<strong>Roo Code — ведущий автономный агент для программирования.</strong> Готовьтесь проектировать, писать код, отлаживать и повышать свою продуктивность так, как вы ещё не видели. Для продолжения работы Roo Code требуется API-ключ.", "notice": "Для начала работы этому расширению нужен провайдер API.", "start": "Поехали!", "chooseProvider": "Выберите провайдера API для начала:", "routers": {"requesty": {"description": "Ваш оптимизированный маршрутизатор LLM", "incentive": "$1 бесплатного кредита"}, "openrouter": {"description": "Унифицированный интерфейс для LLM"}}, "startRouter": "Экспресс-настройка через маршрутизатор", "startCustom": "Использовать свой собственный API-ключ", "telemetry": {"title": "Помогите улучшить Roo Code", "anonymousTelemetry": "Отправлять анонимные данные об ошибках и использовании, чтобы помочь нам исправлять баги и совершенствовать расширение. Код, промпты и личная информация никогда не отправляются.", "changeSettings": "Вы всегда можете изменить это внизу страницы <settingsLink>настроек</settingsLink>", "settings": "настройки", "allow": "Разрешить", "deny": "Запретить"}, "or": "или", "importSettings": "Импорт настроек"}